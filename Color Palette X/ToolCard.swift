//
//  ToolCard.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/12.
//

import SwiftUI

struct ToolCard: View {
    let icon: String
    let title: LocalizedStringKey
    let color: Color
    let action: (() -> Void)?
    let isNavigationLink: Bool
    @State private var isPressed = false

    init(icon: String, title: LocalizedStringKey, color: Color, action: (() -> Void)? = nil, isNavigationLink: Bool = false) {
        self.icon = icon
        self.title = title
        self.color = color
        self.action = action
        self.isNavigationLink = isNavigationLink
    }
    
    var body: some View {
        if isNavigationLink {
            // 当作为 NavigationLink 使用时，不包装 Button
            cardContent
                .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                    isPressed = pressing
                    if pressing {
                        AppLogger.debug("工具卡片按下: \(title)", category: .ui)
                    }
                }, perform: {})
        } else {
            // 普通使用时，包装 Button
            Button(action: {
                AppLogger.info("工具卡片被点击: \(title)", category: .ui)
                action?()
            }) {
                cardContent
            }
            .buttonStyle(PlainButtonStyle())
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
                if pressing {
                    AppLogger.debug("工具卡片按下: \(title)", category: .ui)
                }
            }, perform: {})
        }
    }

    /// 卡片内容视图
    private var cardContent: some View {
        VStack(spacing: 16) {
            // 图标容器
            ZStack {
                Circle()
                    .fill(
                        LinearGradient(
                            colors: [color.opacity(0.2), color.opacity(0.1)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .frame(width: 50, height: 50)

                Image(systemName: icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [color, color.opacity(0.8)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
            }

            // 标题
            Text(title)
                .font(.system(size: 15, weight: .semibold, design: .rounded))
                .foregroundColor(.primary)
                .multilineTextAlignment(.center)
                .lineLimit(2)
                .minimumScaleFactor(0.8)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 140)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(
                    color: color.opacity(0.2),
                    radius: isPressed ? 8 : 12,
                    x: 0,
                    y: isPressed ? 4 : 8
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(
                    LinearGradient(
                        colors: [color.opacity(0.3), color.opacity(0.1)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    lineWidth: 1
                )
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .animation(.easeInOut(duration: 0.1), value: isPressed)
    }
}

#Preview {
    LazyVGrid(columns: [
        GridItem(.flexible(), spacing: 16),
        GridItem(.flexible(), spacing: 16)
    ], spacing: 16) {
        ToolCard(
            icon: "paintbrush.fill",
            title: LocalizedStringKey("gradient_presets"),
            color: .indigo
        )
        
        ToolCard(
            icon: "circle.lefthalf.striped.horizontal",
            title: LocalizedStringKey("color_harmony_generator"),
            color: .purple
        )
    }
    .padding()
}
